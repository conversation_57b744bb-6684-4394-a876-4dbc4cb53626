import Foundation
import Combine

// MARK: - API Configuration
struct APIConfig {
    // Backend服务配置 - 使用真实的FurryKids backend
    static let baseURL = "http://localhost:8000/api/v1"
    static let uploadURL = "http://localhost:8000/uploads"

    // AI服务配置 - 使用backend的AI接口
    static let aiServiceURL = "http://localhost:8000/api/v1/ai"

    // 如果需要直接调用OpenAI (备用)
    static let openAIKey = "your-openai-api-key"
    static let openAIURL = "https://api.openai.com/v1"

    // 认证配置
    static var authToken: String? {
        get {
            return UserDefaults.standard.string(forKey: "auth_token")
        }
        set {
            UserDefaults.standard.set(newValue, forKey: "auth_token")
        }
    }
}

// MARK: - API Errors
enum APIError: Error, LocalizedError {
    case invalidURL
    case noData
    case decodingError
    case networkError(Error)
    case serverError(Int, String?)
    case unauthorized
    case timeout
    case noInternetConnection
    case rateLimited
    case maintenance
    case unknown(String)

    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "请求地址无效"
        case .noData:
            return "服务器没有返回数据"
        case .decodingError:
            return "数据格式错误，请稍后重试"
        case .networkError(let error):
            if error.localizedDescription.contains("offline") {
                return "网络连接已断开，请检查网络设置"
            }
            return "网络连接失败: \(error.localizedDescription)"
        case .serverError(let code, let message):
            if let message = message {
                return message
            }
            switch code {
            case 400:
                return "请求参数错误"
            case 401:
                return "登录已过期，请重新登录"
            case 403:
                return "没有权限执行此操作"
            case 404:
                return "请求的资源不存在"
            case 429:
                return "请求过于频繁，请稍后重试"
            case 500...599:
                return "服务器暂时无法处理请求，请稍后重试"
            default:
                return "服务器错误 (\(code))"
            }
        case .unauthorized:
            return "登录已过期，请重新登录"
        case .timeout:
            return "请求超时，请检查网络连接"
        case .noInternetConnection:
            return "无网络连接，请检查网络设置"
        case .rateLimited:
            return "请求过于频繁，请稍后重试"
        case .maintenance:
            return "服务器正在维护中，请稍后重试"
        case .unknown(let message):
            return "未知错误: \(message)"
        }
    }

    var isRetryable: Bool {
        switch self {
        case .networkError, .timeout:
            return true
        case .serverError(let code, _):
            return code >= 500 || code == 429
        case .noInternetConnection, .rateLimited:
            return true
        default:
            return false
        }
    }

    var shouldLogout: Bool {
        switch self {
        case .unauthorized:
            return true
        default:
            return false
        }
    }
}

// MARK: - Network Manager
class NetworkManager: ObservableObject {
    static let shared = NetworkManager()

    private let session: URLSession
    private var cancellables = Set<AnyCancellable>()

    @Published var isConnected = true
    @Published var isLoading = false
    @Published var connectionQuality: ConnectionQuality = .good

    // Retry configuration
    private let maxRetryAttempts = 3
    private let retryDelay: TimeInterval = 1.0

    enum ConnectionQuality {
        case excellent, good, poor, offline
    }

    private init() {
        // 配置URLSession
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 30
        config.timeoutIntervalForResource = 60
        config.waitsForConnectivity = true

        self.session = URLSession(configuration: config)
        setupNetworkMonitoring()
    }

    private func setupNetworkMonitoring() {
        // 简单的网络监控
        // 在实际项目中可以使用Network framework进行更精确的监控
        Timer.publish(every: 10, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                self?.checkNetworkStatus()
            }
            .store(in: &cancellables)
    }

    private func checkNetworkStatus() {
        // 简单的网络检查
        guard let url = URL(string: APIConfig.baseURL + "/health") else { return }

        URLSession.shared.dataTask(with: url) { [weak self] _, response, error in
            DispatchQueue.main.async {
                if error != nil {
                    self?.isConnected = false
                    self?.connectionQuality = .offline
                } else if let httpResponse = response as? HTTPURLResponse {
                    self?.isConnected = httpResponse.statusCode == 200
                    self?.connectionQuality = httpResponse.statusCode == 200 ? .good : .poor
                }
            }
        }.resume()
    }
    
    // MARK: - Generic Request Method
    func request<T: Codable>(
        endpoint: String,
        method: HTTPMethod = .GET,
        body: Data? = nil,
        headers: [String: String] = [:],
        responseType: T.Type,
        retryCount: Int = 0
    ) -> AnyPublisher<T, APIError> {

        return performRequest(
            endpoint: endpoint,
            method: method,
            body: body,
            headers: headers,
            responseType: responseType
        )
        .catch { [weak self] error -> AnyPublisher<T, APIError> in
            guard let self = self,
                  retryCount < self.maxRetryAttempts,
                  error.isRetryable else {
                return Fail(error: error).eraseToAnyPublisher()
            }

            // 如果是未授权错误，尝试刷新token
            if error.shouldLogout {
                return self.handleUnauthorizedError()
                    .flatMap { _ in
                        self.request(
                            endpoint: endpoint,
                            method: method,
                            body: body,
                            headers: headers,
                            responseType: responseType,
                            retryCount: retryCount + 1
                        )
                    }
                    .eraseToAnyPublisher()
            }

            // 延迟重试
            let delay = self.retryDelay * pow(2.0, Double(retryCount)) // 指数退避

            return Just(())
                .delay(for: .seconds(delay), scheduler: DispatchQueue.main)
                .flatMap { _ in
                    self.request(
                        endpoint: endpoint,
                        method: method,
                        body: body,
                        headers: headers,
                        responseType: responseType,
                        retryCount: retryCount + 1
                    )
                }
                .eraseToAnyPublisher()
        }
        .eraseToAnyPublisher()
    }

    // MARK: - Core Request Implementation
    private func performRequest<T: Codable>(
        endpoint: String,
        method: HTTPMethod,
        body: Data?,
        headers: [String: String],
        responseType: T.Type
    ) -> AnyPublisher<T, APIError> {

        guard let url = URL(string: APIConfig.baseURL + endpoint) else {
            return Fail(error: APIError.invalidURL)
                .eraseToAnyPublisher()
        }

        var request = URLRequest(url: url)
        request.httpMethod = method.rawValue
        request.httpBody = body

        // 默认headers
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("application/json", forHTTPHeaderField: "Accept")

        // 添加JWT认证token
        if let token = APIConfig.authToken {
            request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        }

        // 添加自定义headers
        for (key, value) in headers {
            request.setValue(value, forHTTPHeaderField: key)
        }

        return session.dataTaskPublisher(for: request)
            .timeout(.seconds(30), scheduler: DispatchQueue.main)
            .tryMap { data, response in
                // 检查HTTP状态码
                if let httpResponse = response as? HTTPURLResponse {
                    let statusCode = httpResponse.statusCode

                    switch statusCode {
                    case 401:
                        throw APIError.unauthorized
                    case 429:
                        throw APIError.rateLimited
                    case 503:
                        throw APIError.maintenance
                    case 400...499:
                        // 尝试解析错误消息
                        let errorMessage = self.parseErrorMessage(from: data)
                        throw APIError.serverError(statusCode, errorMessage)
                    case 500...599:
                        throw APIError.serverError(statusCode, "服务器内部错误")
                    default:
                        break
                    }
                }
                return data
            }
            .decode(type: responseType, decoder: self.createJSONDecoder())
            .mapError { error in
                if error is DecodingError {
                    return APIError.decodingError
                } else if let apiError = error as? APIError {
                    return apiError
                } else if (error as NSError).code == NSURLErrorTimedOut {
                    return APIError.timeout
                } else if (error as NSError).code == NSURLErrorNotConnectedToInternet {
                    return APIError.noInternetConnection
                } else {
                    return APIError.networkError(error)
                }
            }
            .eraseToAnyPublisher()
    }

    // MARK: - Error Handling
    private func parseErrorMessage(from data: Data) -> String? {
        do {
            if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
               let message = json["message"] as? String {
                return message
            }
        } catch {
            // 忽略解析错误，返回nil
        }
        return nil
    }

    private func handleUnauthorizedError() -> AnyPublisher<Void, APIError> {
        // 尝试刷新token
        return AuthService.shared.refreshToken()
            .map { _ in () }
            .catch { _ in
                // 刷新失败，执行登出
                AuthService.shared.logoutLocally()
                return Fail<Void, APIError>(error: APIError.unauthorized)
            }
            .eraseToAnyPublisher()
    }

    // MARK: - JSON Decoder Configuration
    private func createJSONDecoder() -> JSONDecoder {
        let decoder = JSONDecoder()
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'"
        formatter.timeZone = TimeZone(secondsFromGMT: 0)
        decoder.dateDecodingStrategy = .formatted(formatter)
        return decoder
    }

    // MARK: - Network Status
    func isNetworkAvailable() -> Bool {
        return isConnected
    }

    func getConnectionQuality() -> ConnectionQuality {
        return connectionQuality
    }
    
    // MARK: - Upload Method (for images)
    func uploadImage(
        endpoint: String,
        imageData: Data,
        fileName: String
    ) -> AnyPublisher<UploadResponse, APIError> {
        
        guard let url = URL(string: APIConfig.baseURL + endpoint) else {
            return Fail(error: APIError.invalidURL)
                .eraseToAnyPublisher()
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        
        let boundary = UUID().uuidString
        request.setValue("multipart/form-data; boundary=\(boundary)", forHTTPHeaderField: "Content-Type")
        
        let body = createMultipartBody(boundary: boundary, imageData: imageData, fileName: fileName)
        request.httpBody = body
        
        return session.dataTaskPublisher(for: request)
            .map(\.data)
            .decode(type: UploadResponse.self, decoder: JSONDecoder())
            .mapError { error in
                if error is DecodingError {
                    return APIError.decodingError
                } else {
                    return APIError.networkError(error)
                }
            }
            .eraseToAnyPublisher()
    }
    
    private func createMultipartBody(boundary: String, imageData: Data, fileName: String) -> Data {
        var body = Data()
        
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"image\"; filename=\"\(fileName)\"\r\n".data(using: .utf8)!)
        body.append("Content-Type: image/jpeg\r\n\r\n".data(using: .utf8)!)
        body.append(imageData)
        body.append("\r\n--\(boundary)--\r\n".data(using: .utf8)!)
        
        return body
    }
}

// MARK: - HTTP Methods
enum HTTPMethod: String {
    case GET = "GET"
    case POST = "POST"
    case PUT = "PUT"
    case DELETE = "DELETE"
    case PATCH = "PATCH"
}

// MARK: - Response Models
struct UploadResponse: Codable {
    let success: Bool
    let imageUrl: String?
    let message: String?
}

struct APIResponse<T: Codable>: Codable {
    let success: Bool
    let data: T?
    let message: String?
    let error: String?
} 